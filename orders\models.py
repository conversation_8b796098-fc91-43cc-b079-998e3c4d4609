from django.db import models
from django.conf import settings
from decimal import Decimal
from django.utils import timezone
from catalogue.models import Service
import uuid


class Order(models.Model):
    """
    Main order model tracking the complete lifecycle of a service order.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending Payment'),
        ('confirmed', 'Confirmed'),
        ('assigned', 'Assigned to Provider'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]
    
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]
    
    PAYMENT_METHOD_CHOICES = [
        ('razorpay', 'Razorpay'),
        ('cod', 'Cash on Delivery'),
    ]

    # Order identification
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order_number = models.CharField(max_length=20, unique=True, blank=True)
    
    # Customer information
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='orders',
        db_constraint=False  # Cross-database foreign key
    )
    
    # Order status and tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='razorpay')
    
    # Financial information
    subtotal = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    minimum_order_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Coupon information
    coupon_code = models.CharField(max_length=50, blank=True, null=True)
    coupon_discount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Address information (stored as JSON or separate model)
    delivery_address = models.JSONField(help_text="Customer's delivery address")
    
    # Service provider assignment
    assigned_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_orders',
        limit_choices_to={'user_type': 'provider'},
        db_constraint=False  # Cross-database foreign key
    )
    
    # Scheduling
    scheduled_date = models.DateField(null=True, blank=True)
    scheduled_time_slot = models.CharField(max_length=50, blank=True, null=True)
    
    # Payment tracking
    payment_id = models.CharField(max_length=100, blank=True, null=True)
    payment_signature = models.CharField(max_length=200, blank=True, null=True)
    
    # Notes and special instructions
    customer_notes = models.TextField(blank=True, null=True)
    admin_notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['assigned_provider', 'status']),
            models.Index(fields=['order_number']),
            models.Index(fields=['created_at']),
        ]

    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)

    def generate_order_number(self):
        """Generate unique order number"""
        import random
        import string
        timestamp = timezone.now().strftime('%Y%m%d')
        random_part = ''.join(random.choices(string.digits, k=4))
        return f"HS{timestamp}{random_part}"

    def can_be_cancelled(self):
        """Check if order can be cancelled"""
        return self.status in ['pending', 'confirmed']

    def can_be_rescheduled(self):
        """Check if order can be rescheduled"""
        return self.status in ['confirmed', 'assigned']

    def __str__(self):
        return f"Order {self.order_number} - {self.customer.mobile}"


class OrderItem(models.Model):
    """
    Individual items within an order.
    """
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    service = models.ForeignKey(Service, on_delete=models.CASCADE, db_constraint=False)  # Cross-database foreign key
    quantity = models.PositiveIntegerField(default=1)
    
    # Price at time of order (for historical accuracy)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_per_unit = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Service-specific details
    estimated_duration = models.DurationField(null=True, blank=True)
    special_instructions = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['id']

    def save(self, *args, **kwargs):
        if not self.total_price:
            self.total_price = (self.unit_price - self.discount_per_unit) * self.quantity
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.quantity} x {self.service.title} in Order {self.order.order_number}"


class OrderStatusHistory(models.Model):
    """
    Track order status changes for audit trail.
    """
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='status_history')
    previous_status = models.CharField(max_length=20, blank=True, null=True)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_constraint=False  # Cross-database foreign key
    )
    reason = models.TextField(blank=True, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"Order {self.order.order_number}: {self.previous_status} → {self.new_status}"


class OrderCancellation(models.Model):
    """
    Track order cancellations with reasons.
    """
    CANCELLATION_REASONS = [
        ('customer_request', 'Customer Request'),
        ('provider_unavailable', 'Provider Unavailable'),
        ('payment_failed', 'Payment Failed'),
        ('service_unavailable', 'Service Unavailable'),
        ('other', 'Other'),
    ]
    
    order = models.OneToOneField(Order, on_delete=models.CASCADE, related_name='cancellation')
    reason = models.CharField(max_length=30, choices=CANCELLATION_REASONS)
    description = models.TextField(blank=True, null=True)
    cancelled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_constraint=False  # Cross-database foreign key
    )
    refund_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    refund_processed = models.BooleanField(default=False)
    cancelled_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Cancellation for Order {self.order.order_number}"


class OrderReschedule(models.Model):
    """
    Track order rescheduling requests.
    """
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='reschedules')
    original_date = models.DateField()
    original_time_slot = models.CharField(max_length=50)
    new_date = models.DateField()
    new_time_slot = models.CharField(max_length=50)
    reason = models.TextField(blank=True, null=True)
    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        db_constraint=False  # Cross-database foreign key
    )
    approved = models.BooleanField(default=False)
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_reschedules',
        db_constraint=False  # Cross-database foreign key
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Reschedule for Order {self.order.order_number}"
